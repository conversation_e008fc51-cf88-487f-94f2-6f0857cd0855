// pages/advertising/advertising.js
// let P8SDK = require('../../p8sdk-wechat-mini-ad.js')
let app = getApp();
var rtime;
var wx_videoAD;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    form: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('激励视屏请求参数：extTran', options);
    let params = {}
    if (options.params) {
      console.warn('extad跳转extTranAd传值', JSON.parse(options.params))
      params = JSON.parse(options.params)
    }
    console.log('params>>>>>>', params);
    rtime = params.time
    let that = this
    that.setData({
      form: params
    })
    // h5激励视频
    // if (params.gotoPay_type === 'h5') {
    //   P8SDK.wxADinit(params.adUitld)
    //   P8SDK.videoADShow(that)
    // }

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this
    console.error('触发onReady激励视频',that.data.form);
    if (that.data.form.gotoPay_type === 'h5') {
      that.wxADinit(that.data.form.adUitld)
      that.videoADShow(that)
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.error('关闭小程序，或者隐藏小程序');
    wx_videoAD.destroy(() => {
      console.log("激励视频 广告销毁激励视频成功");
    });
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  // 看完激励视频奖励
  isADsureOk() {
    console.log('rtime>>>>>', rtime);
    let params = {
      type: 'rewardVedio',
      code: 0,
      msg: '看完激励视频',
      time: rtime
    }
    // app.globalData.webViewPath = encodeURI(app.globalData.webViewOriginPath + `#${JSON.stringify(params)}`)
    this.back(params);

  },
  // 未看完激励视频奖励
  isADNotOk() {
    console.log('rtime>>>>>', rtime);
    let params = {
      type: 'rewardVedio',
      code: 1,
      msg: ' 未看完激励视频',
      time: rtime
    }
    // app.globalData.webViewPath = encodeURI(app.globalData.webViewOriginPath + `#${JSON.stringify(params)}`)
    this.back(params);
  },
  back(params) {
    console.log('back------->params', params);
    var pages = getCurrentPages(); //当前页面
    var prevPage = pages[pages.length - 2]; //上个页面
    // 直接给上一个页面赋值
    prevPage.setData({
      params: params
    });
    wx.navigateBack({
      delta: 1,
    })
    // wx.navigateBackMiniProgram({
    //   extraData: params,
    //   success(res) {
    //     // 返回成功
    //   }
    // })
  },
  /* 激励视频初始化 */
  wxADinit(e, t, o, n) {
    if (e) {
      console.log("激励视频id ", e);
      wx_videoAD = wx.createRewardedVideoAd({
        adUnitId: e,
        multiton: true, // 多个广告单例 多次创建广告id会返回不同的实例（不同的激励广告id数据会正常） 会造成内存增加
      });
      wx_videoAD.onLoad(() => {
        console.log("激励视频 广告加载成功");
      });
      wx_videoAD.onError((e) => {
        console.log("激励视频 广告加载异常", e);
      });
    }
  },
  /* 激励视频激活显示 */
  videoADShow(that) {
    if (!wx_videoAD || !wx_videoAD.show) {
      console.log("激励视频不存在");
      return;
    }
    wx_videoAD.show().catch((e) => {
      console.log("激励视频 广告加载异常 再次广告加载", e);
      wx_videoAD.load().then(() => wx_videoAD.show());
    });
    const i = (e) => {
      wx_videoAD.offClose(i);
      if ((e && e.isEnded) || e === undefined) {
        console.log("正常播放结束，可以下发游戏奖励");
        that.isADsureOk()
      } else {
        console.log("播放中途退出，不下发游戏奖励");
        that.isADNotOk()
      }
    };
    wx_videoAD.onClose(i);
  },
})