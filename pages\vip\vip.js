// pages/vip/vip.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    baseUrl: "https://recharge.yyingplay.com",
    queryUrl: "/sy/activityVipKf",
    path: "",
    site: "",
    imgPath: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this
    let obj = wx.getEnterOptionsSync();
    console.error('-----------onLoadgetEnterOptionsSync-webview', obj);
    let site = "";
    if (obj.referrerInfo.extraData) {
      site = obj.referrerInfo.extraData.site;
    }
    that.setData({
      site: site,
    })
    let url = `${that.data.baseUrl}${that.data.queryUrl}`
    console.log('url', url);
    console.log('this.data.site', that.data.site);
    let rurl = url + '?' + that.data.site
    console.log('rurl', rurl);

    let isVipActive = wx.getStorageSync('isVipActive')
    if (isVipActive) {
      wx.redirectTo({
        url: '/pages/index/index',
      })

      return;
    }

    if (that.data.site) {
      wx.request({
        url: url,
        data: {
          site: that.data.site,
        },
        method: "GET",
        success(res) {
          console.log(res.data)
          let imgUrl = res.data.url
          console.log('imgUrl', imgUrl);
          that.setData({
            imgPath: imgUrl,
          })
          wx.setStorageSync('isVipActive', true)
        },
        fail(err) {
          wx.showToast({
            title: '服务器异常，获取不到图片',
            icon: "none"
          })
        }
      })
    } else {
      wx.showToast({
        title: 'site没配置或者site不存在',
        icon: "none"
      })
    }

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  checkImage() {
    let that = this
    wx.previewImage({
      current: that.data.imgPath, // 当前显示图片的http链接
      urls: [that.data.imgPath], // 需要预览的图片http链接列表
      showmenu: true,
    })
  }
})