// pages/advertising/advertising.js
// let P8SDK = require('../../p8sdk-wechat-mini-ad.js')
let app = getApp();
var rtime;
Page({

  /**
   * 页面的初始数据
   */
  data: {},

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('激励视屏请求参数：', options);
    rtime = options.time
    let that = this
    // h5激励视频
    if (options.gotoPay_type === 'h5') {
      P8SDK.wxADinit(options.adUitld)
      P8SDK.videoADShow(that)
    }

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  // 看完激励视频奖励
  isADsureOk() {
    let params = {
      type: 'rewardVedio',
      code: 0,
      msg: '看完激励视频',
      time: rtime
    }
    app.globalData.webViewPath = encodeURI(app.globalData.webViewOriginPath + `#${JSON.stringify(params)}`)
    this.back();

  },
  // 未看完激励视频奖励
  isADNotOk() {
    let params = {
      type: 'rewardVedio',
      code: 1,
      msg: ' 未看完激励视频',
      time: rtime
    }
    app.globalData.webViewPath = encodeURI(app.globalData.webViewOriginPath + `#${JSON.stringify(params)}`)
    this.back();
  },
  back() {
    wx.navigateBack({
      delta: 0,
    })
  }
})