// pages/assistant/assistant.js
Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this
    let appid = wx.getAccountInfoSync().miniProgram.appId
    if (appid == "wxbf8b8fa70e37d5a2") {
      that.setData({
        backgroundImageUrl: "https://dhbhtoss.jvplay.cn/siyu/assistant/assistantTX.jpg"
      })
    } else {
      that.setData({
        backgroundImageUrl: "https://dhbhtoss.jvplay.cn/siyu/assistant/assistantYZ.jpg"
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          path: `/pages/assistant/assistant`,
          webpageUrl: that.data.backgroundImageUrl,
        })
      }, 2000)
    })
    return {
      path: `/pages/assistant/assistant`,
      webpageUrl: that.data.backgroundImageUrl,
      promise
    }
  },
  onShareTimeline() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({})
      }, 2000)
    })
    return {
      promise
    }
  },
  checkImage() {
    let that = this
    wx.previewImage({
      current: that.data.backgroundImageUrl, // 当前显示图片的http链接
      urls: [that.data.backgroundImageUrl], // 需要预览的图片http链接列表
      showmenu: true,
    })
  }
})