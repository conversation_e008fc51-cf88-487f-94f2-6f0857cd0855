.index-container {
  width: 100vw;
  height: 100vh;
  background-color: #1f1925;
  position: relative;
  vertical-align: middle;

}

.index-container-bg {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 1;
  vertical-align: middle;
}

.index-container-img {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  vertical-align: middle;
}

.menu-container {
  width: 100%;
  height: 160rpx;
  position: absolute;
  top: 22%;
  z-index: 2;
}

.menu-list {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.menu-item {
  width: calc(100% / 4);
  /* 改为4等分，让前4个正常显示 */
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  /* 防止压缩 */
}

/* 最后一个菜单项只显示一半 */
.menu-item-half {
  width: calc(100% / 8);
  /* 第5个只显示一半宽度 */
  overflow: hidden;
  /* 隐藏超出部分 */
}

.menu-title {
  position: absolute;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
  bottom: 22rpx;
}

.menu-item-bg {
  width: 138rpx;
  height: 156rpx;
  object-fit: cover;
}

.menu-item-icon {
  position: absolute;
  object-fit: contain;
  margin-left: 8rpx;
}

/* 菜单选中状态样式 */
.menu-item-selected {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

.menu-item-selected .menu-item-bg {
  filter: brightness(1.2) saturate(1.3);
}

.menu-item-selected .menu-title {
  color: #ffd700;
  text-shadow: 0 0 8rpx rgba(255, 215, 0, 0.6);
}

.slider-container {
  width: 100%;
  height: 40rpx;
  position: absolute;
  top: 33%;
  z-index: 2;
}

.slider-list {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-item {
  width: 16rpx;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10rpx;
}

.slider-dot {
  width: 16rpx;
  height: 14rpx;
  object-fit: cover;
}

.info-container {
  width: 100%;
  position: absolute;
  background-color: #1f1925;
  top: 36%;
  z-index: 3;
  /* padding-bottom: 100rpx; */
}

.info-container-bg {
  width: calc(100% - 40rpx);
  height: 1028rpx;
  padding: 0 20rpx;
}

.info-container-img {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  vertical-align: middle;
}

.info-list {
  width: calc(100% - 160rpx);
  height: calc(100% - 70rpx);
  overflow-y: auto;
  /* overflow: hidden; */
  position: absolute;
  top: 0;
  padding: 0 80rpx;
  margin: 40rpx 0;
}

.info-item {
  position: relative;
  margin-bottom: 120rpx;
  z-index: 1999;
}

.info-item-title {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.info-item-subTitle {
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.info-item-image-bg {
  width: 105%;
  height: 248rpx;
  position: absolute;
  z-index: 4;
  left: -15rpx;
  bottom: -10rpx;
}

.info-item-image {
  width: 100%;
  height: 216rpx;
  position: relative;
  z-index: 5;
}

.info-item-image-xt {
  width: 100%;
  height: 16rpx;
  position: absolute;
  bottom: -120rpx;
  left: 0;
}

.info-container-content {
  color: #a5afc0;
  font-size: 24rpx;
  position: absolute;
  bottom: -80rpx;
}

/* 无数据提示样式 */
.no-data-tip {
  width: 100%;
  text-align: center;
  padding: 100rpx 0;
  color: #a5afc0;
  font-size: 28rpx;
}