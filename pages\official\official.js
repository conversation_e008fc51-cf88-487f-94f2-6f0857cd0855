// pages/official/official.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    baseUrl: "https://dhbhtoss.jvplay.cn",
    queryUrl: "/siyu/private",
    path: "",
    site: "",
    imgPath: "",
    imgKey: 0,
    imgArray: [{
      value: "wjyzcswxxyx",
    }, {
      value: "wjtxdwxxyx"
    }]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this

    let url = `${that.data.baseUrl}${that.data.queryUrl}`
    let rurl = `${url}/${that.data.imgArray[that.data.imgKey].value}.jpg`

    that.setData({
      imgPath: rurl
    })

    let isOfficialActive = wx.getStorageSync('isOfficialActive')
    if (isOfficialActive) {
      wx.redirectTo({
        url: '/pages/index/index',
      })

      return;
    }

    wx.setStorageSync('isOfficialActive', true)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})