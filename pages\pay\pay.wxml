<!--pages/pay/pay.wxml-->
<view class="containers">
  <view class="payStaus">支付状态：{{gotoPay_payStatus === 1 ?'已支付':'未支付'}}</view>
  <button type="default" class="sumbit-btn" bindtap="payBtn">开始支付</button>
  <button type="primary" class="backToGame-btn" bindtap="backToGame">返回首页</button>
  <view class="gameDetail">
    <view class="gameContent">
      <view class="gameMessage">购买商品：{{gotoPay_goods}}</view>
      <view class="gameMessage">商品金额：{{gotoPay_price}}</view>
      <!-- <view class="gameMessage">商品金额：<text class="originalCost">{{gotoPay_price}}元</text></view> -->
      <!-- <view class="gameMessage">应付金额：<text class="nicePrice">{{gotoPay_price * discount / 100}}</text></view> -->
      <view class="gameMessage">订单：<text class="fsNormal">{{gotoPay_order}}</text></view>
      <view class="gameMessage">角色：{{gotoPay_character}}</view>
      <view class="gameMessage">区服：{{gotoPay_area}}</view>
    </view>
  </view>
</view>