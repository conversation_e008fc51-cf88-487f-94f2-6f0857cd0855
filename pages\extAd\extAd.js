// pages/advertising/advertising.js
// let P8SDK = require('../../p8sdk-wechat-mini-ad.js')
let app = getApp();
var rtime;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    params: {
      type: '',
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('激励视屏请求参数：', options);
    let that = this
    that.setData({
      'params.type': '',
    })
    console.error('this.data.params>>>>onLoad', that.data.params);
    // h5激励视频
    if (options.gotoPay_type === 'h5') {
      // P8SDK.wxADinit(options.adUitld)
      // P8SDK.videoADShow(that)
    }

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    let that = this
    let obj = wx.getEnterOptionsSync()
    console.log('onShow>>>>>>getEnterOptionsSync', obj);
    console.error('this.data.params>>>>onShow', that.data.params);
    if (that.data.params.type == 'rewardVedio') {
      that.back(that.data.params);
    } else {
      let data = ''
      // h5激励视频
      console.log('加载激励视频前》》》》》》');
      if (obj.referrerInfo.appId && obj.referrerInfo.extraData) {
        console.log('加载激励视频appid');
        data = JSON.parse(JSON.stringify(obj.referrerInfo.extraData))
        console.log('data>>>>>>>', data);
        rtime = data.time
        if (data.gotoPay_type === 'h5') {
          console.log('加载激励视频》》》》》');
          // P8SDK.wxADinit(data.adUitld)
          // P8SDK.videoADShow(that)
          let params = JSON.stringify(data)
          wx.navigateTo({
            url: `/pages/extTranAd/extTranAd?params=${params}`,
          })
        }
      }
    }


  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  // 看完激励视频奖励
  isADsureOk() {
    console.log('rtime>>>>>', rtime);
    let params = {
      type: 'rewardVedio',
      code: 0,
      msg: '看完激励视频',
      time: rtime
    }
    // app.globalData.webViewPath = encodeURI(app.globalData.webViewOriginPath + `#${JSON.stringify(params)}`)
    this.back(params);

  },
  // 未看完激励视频奖励
  isADNotOk() {
    console.log('rtime>>>>>', rtime);
    let params = {
      type: 'rewardVedio',
      code: 1,
      msg: ' 未看完激励视频',
      time: rtime
    }
    // app.globalData.webViewPath = encodeURI(app.globalData.webViewOriginPath + `#${JSON.stringify(params)}`)
    this.back(params);
  },
  back(params) {
    console.log('back------->params', params);
    wx.navigateBackMiniProgram({
      extraData: params,
      success(res) {
        // 返回成功
      }
    })
  }
})