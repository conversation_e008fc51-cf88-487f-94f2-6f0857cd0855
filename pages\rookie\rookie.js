// pages/rookie/rookie.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    customBarHeight: 0,
    statusBarHeight: 0,
    menuButtonTop: 0,
    navBarHeight: 0,
    menuButtonHeight: 0,
    navBarTitle: "",
    query: {},
    id: 0,
    backgroundImageUrl: "",
    infoList: [{
        id: 1,
        title: "新手攻略",
        subTitle: "新手首日快速通过技巧",
        imgUrl: "../../assets/image/banner_xsgl_01.png",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/rookie/rookieDetail.png",
        content: "西游道阻且长，伏魔之路漫漫·····此番西游伏魔众妖聚首，他们各个都身怀绝技，本领过人而本次",
        type: "rookie"
      },
      {
        id: 2,
        title: "艾丹",
        subTitle: "暗夜英雄秘传 影舞者·艾丹",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/aidan/aidanBanner.png",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/role/aidan/aidanDetail.jpg",
        content: "以一敌百，成功守护精灵族的圣地，族人心中的传奇英雄，他又有着怎么样神秘的能力呢?",
        type: "profession"
      },
      {
        id: 3,
        title: "菲欧娜",
        subTitle: "站桩输出流 吸血鬼·菲欧娜",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/fiona/fionaBanner.jpg",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/role/fiona/fionaDetail.jpg",
        content: "东方装束的包子头吸血鬼?纤弱的少女竟然肉过牛头人?离谱的人设搭配更加离谱的躺平流派设计",
        type: "profession"
      },
      {
        id: 4,
        title: "维克托",
        subTitle: "法术机关枪 蒸汽机器人·维克托",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/victory/victoryBanner.jpg",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/role/victory/victoryDetail.jpg",
        content: "机械降神维克托，主神圣元素伤害",
        type: "profession"
      },
      {
        id: 5,
        title: "影",
        subTitle: "带狼高富帅 限定英雄·影",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/shadow/shadowBanner.png",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/role/shadow/shadowDetail.jpg",
        content: "游戏首个传奇级别英雄，自带召唤物【雷牙】",
        type: "profession"
      },
      {
        id: 6,
        title: "符文",
        subTitle: "萌新向符文系统详情",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/rune/runeBanner.jpg",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/rune/runeDetail.jpg",
        content: "符文系统是游戏中的一个重要系统，玩家可以通过收集符文来提升自己的战斗力",
        type: "rookie"
      },
      {
        id: 7,
        title: "装备",
        subTitle: "进阶向装备构筑详解",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/gear/gearBanner.jpg",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/gear/gearDetail.jpg",
        content: "餐前提醒，二件套攻略不是噱头但仅适用于初入深渊章节，处于红装向粉装过渡阶段的玩家，此阶段之前还是套装更强",
        type: "rookie"
      },
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this;
    if (options.title) {
      that.setData({
        navBarTitle: options.title
      })
    }

    if (options.id) {
      that.setData({
        query: options,
        id: options.id
      })
    }

    wx.setNavigationBarTitle({
      title: that.data.navBarTitle
    })

    that.setData({
      backgroundImageUrl: that.data.infoList.find(item => item.id == that.data.id).detailImageUrl
    })

    const systemInfo = wx.getWindowInfo();

    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 状态栏高度（手机顶部状态栏区域的高度）

    const statusBarHeight = systemInfo.statusBarHeight;

    // 胶囊按钮距离顶部的距离（通常比状态栏高度多一点点，具体以获取到的为准）

    const menuButtonTop = menuButtonInfo.top;

    // 导航栏高度 = 胶囊按钮高度 + (胶囊按钮上边距 - 状态栏高度) * 2

    // 因为胶囊按钮上下有间隙，所以导航栏高度可以这样计算：

    const navBarHeight = (menuButtonTop - statusBarHeight) * 2 + menuButtonInfo.height;

    const menuButtonHeight = menuButtonInfo.height;

    // 整个自定义导航栏的高度 = 状态栏高度 + 导航栏高度

    const customBarHeight = statusBarHeight + navBarHeight;

    that.setData({
      customBarHeight,
      statusBarHeight,
      menuButtonTop,
      navBarHeight,
      menuButtonHeight
    })


  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // wx.hideHomeButton();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          path: `/pages/index/index`,
          webpageUrl: that.data.backgroundImageUrl,
        })
      }, 2000)
    })
    return {
      path: `/pages/index/index`,
      webpageUrl: that.data.backgroundImageUrl,
      promise
    }
  },
  onShareTimeline() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({})
      }, 2000)
    })
    return {
      promise
    }
  },
  gotoBack() {
    wx.navigateBack();
  }
})