// pages/pay/pay.js
// let P8SDKConfig = require('../../p8sdk-wechat-config.js')
// let appid = P8SDKConfig.appid
Page({

  /**
   * 页面的初始数据
   */
  data: {
    gotoPay_payStatus: 0,
    gotoPay_goods: '',
    gotoPay_price: '',
    gotoPay_order: '',
    gotoPay_character: '',
    gotoPay_area: '',
    gotoPay_site: '',
    openid: '',
    session_key: '',
    gotoPay_payData: {},
    loginCode: '',
    appid: '',
    baseUrl: 'https://center.lekplay.cn',
    basePayUrl: 'https://recharge.lekplay.cn',
    sceneUrl: 'https://recharge.lekplay.cn',
    scene: '',
    p8site: '',
    p8key: '',
    discount: 100,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let _this = this
    wx.hideHomeButton({
      complete: () => { }
    })
    wx.showLoading({
      title: '正在拉起支付中...',
      mask: true,
    })
    console.warn(options);
    console.error('scene', options.scene);
    const scene = decodeURIComponent(options.scene)
    this.setData({
      scene: scene,
    })
    wx.getSystemInfo({
      success: function (res) {
        console.log('获取到的设备信息', res)
        console.warn('config配置项信息', P8SDKConfig);
        if (res.platform == "ios") {
          _this.setData({
            p8site: P8SDKConfig.site_ios,
            p8key: P8SDKConfig.key_ios,
          })
        } else {
          _this.setData({
            p8site: P8SDKConfig.site_android,
            p8key: P8SDKConfig.key_android,
          })
        }
      }
    });
    _this.discountFun()
    console.error('onLoad', _this.data)
    if (options.scene) {
      wx.request({
        url: _this.data.sceneUrl + scene,
        method: "GET",
        success: function (res) {
          console.error('scene接口', res)
          if (res.data.result == 0) {
            _this.setData({
              gotoPay_goods: res.data.data.gotoPay_goods,
              gotoPay_character: res.data.data.gotoPay_character,
              gotoPay_area: res.data.data.gotoPay_area,
              gotoPay_order: res.data.data.gotoPay_order,
              gotoPay_price: res.data.data.gotoPay_price,
              gotoPay_site: res.data.data.gotoPay_site,
            })
          }
          console.error('二维码请求scene返回的参数', _this.data, res)
        }
      })
    } else if (options.gotoPay_type === 'h5') {
      console.log('接收订单参数', options)
      _this.setData({
        gotoPay_goods: options.gotoPay_goods,
        gotoPay_character: options.gotoPay_character,
        gotoPay_area: options.gotoPay_area,
        gotoPay_order: options.gotoPay_order,
        gotoPay_price: options.gotoPay_price,
        gotoPay_site: options.gotoPay_site,
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let _this = this
    console.error('检查参数', _this.data)
    wx.login({
      success(res) {
        if (res.code) {
          //发起网络请求
          wx.request({
            url: _this.data.baseUrl + '/oauth/jscode2Session_two',
            data: {
              js_code: res.code,
              site: _this.data.p8site,
              appid: appid,
            },
            method: "POST",
            success: function (res) {
              console.error('/oauth/jscode2Session_two', res)
              if (res.data.result == 0) {
                let openData = JSON.parse(res.data.data)
                _this.setData({
                  openid: openData.openid
                })
                console.error('请求支付接口', _this.data)
                wx.setStorageSync('openid', _this.data.openid)
                wx.setStorageSync('site', _this.data.p8site)
                wx.setStorageSync('key', _this.data.p8key)
                let payConfig = {
                  pay_type: 16,
                  openid: _this.data.openid,
                  order_id: _this.data.gotoPay_order,
                  appid: appid,
                }
                let payUrl = `${_this.data.basePayUrl}/toPay`
                // let payUrl = `https://trecharge.play800.cn/toPay`
                console.log('支付请求的参数', JSON.stringify(payConfig))
                console.log('支付请求url', payUrl)
                let o = payUrl + "?" + _this.keyValueConnect(payConfig);
                console.log("支付请求拼接Url: ", o);
                wx.request({
                  url: payUrl,
                  data: payConfig,
                  method: "GET",
                  success: function (res) {
                    console.error('res', res)
                    let payData = res.data
                    _this.setData({
                      gotoPay_payData: payData
                    })
                    console.error('支付请求的参数', payConfig)
                    console.error('支付返回的参数', payData)
                    if (res.data.state == 1) {
                      wx.hideLoading()
                      wx.requestPayment({
                        timeStamp: payData.timeStamp,
                        nonceStr: payData.nonceStr,
                        package: payData.package,
                        signType: payData.signType,
                        paySign: payData.paySign,
                        success(res) {
                          wx.showLoading({
                            title: '查询支付结果...',
                            mask: true,
                          })
                          setTimeout(() => {
                            wx.hideLoading()
                            wx.showToast({
                              title: '支付成功',
                              icon: 'success',
                              duration: 800,
                              mask: true
                            })
                            _this.setData({
                              gotoPay_payStatus: 1,
                            })
                            wx.showModal({
                              title: '支付成功',
                              content: '支付成功请返回首页',
                              showCancel: false,
                              confirmText: '返回首页',
                              success(res) {
                                if (res.confirm) {
                                  wx.exitMiniProgram({
                                    success: function () { },
                                    fail: function () { }
                                  })
                                } else if (res.cancel) {

                                }
                              }
                            })
                          }, 3000)
                        },
                        fail(res) {
                          wx.showToast({
                            title: '取消支付',
                            icon: 'error',
                            duration: 500,
                            mask: true
                          })
                        },
                        complete(res) {
                          if (res.errMsg == 'requestPayment:cancel') {
                            wx.showToast({
                              title: '取消支付',
                              icon: 'error',
                              duration: 500,
                              mask: true
                            })
                          }
                        }
                      })
                    } else {
                      wx.showToast({
                        title: res.data.err_code_des || res.data.return_msg || '订单参数错误',
                        icon: 'none',
                        duration: 1000,
                        mask: true
                      })
                    }

                  }
                })
              } else {
                wx.showToast({
                  title: '获取openid失败！请找技术人员',
                  icon: 'none',
                  duration: 1000,
                  mask: true
                })
                console.error('获取openid失败原因', res.data)
              }
            }
          })
        } else {
          console.log('wx.login登录失败！' + res.errMsg)
        }
      }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(event) {
    let _this = this
    let obj = wx.getEnterOptionsSync()
    let data = ''
    if (obj.referrerInfo.appId && obj.referrerInfo.extraData) {
      data = JSON.parse(JSON.stringify(obj.referrerInfo.extraData))
      if (!(_this.data.gotoPay_goods)) {
        this.setData({
          gotoPay_goods: data.gotoPay_goods,
          gotoPay_price: data.gotoPay_price,
          gotoPay_order: data.gotoPay_order,
          gotoPay_character: data.gotoPay_character,
          gotoPay_area: data.gotoPay_area,
          gotoPay_site: data.gotoPay_site
        })
      }
    } else {
      if (_this.data.gotoPay_payStatus == 0 && !_this.data.scene) {
        wx.showToast({
          title: '获取订单参数失败！请重新回首页下单',
          icon: 'none',
          duration: 2000,
          mask: true
        })
      } else {

      }
    }
    console.error('onShow', _this.data)
  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    let _this = this
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    let _this = this
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  backToGame() {
    wx.exitMiniProgram({
      success: function () { },
      fail: function () { }
    })
  },
  payBtn() {
    let _this = this
    console.error('点击开始支付按钮', _this.data);
    let payConfig = {
      pay_type: 16,
      openid: _this.data.openid,
      order_id: _this.data.gotoPay_order,
      appid: appid,
    }
    let payUrl = `${_this.data.basePayUrl}/toPay`
    // let payUrl = `https://trecharge.play800.cn/toPay`
    console.log('支付请求的参数', JSON.stringify(payConfig))
    console.log('支付请求url', payUrl)
    let o = payUrl + "?" + _this.keyValueConnect(payConfig);
    console.log("支付请求拼接Url: ", o);
    wx.request({
      url: payUrl,
      data: payConfig,
      method: "GET",
      success: function (res) {
        let payData = res.data
        if (res.data.state == 1) {
          wx.requestPayment({
            timeStamp: payData.timeStamp,
            nonceStr: payData.nonceStr,
            package: payData.package,
            signType: payData.signType,
            paySign: payData.paySign,
            success(res) {
              wx.showLoading({
                title: '查询支付结果...',
                mask: true,
              })
              setTimeout(() => {
                wx.hideLoading()
                wx.showToast({
                  title: '支付成功',
                  icon: 'success',
                  duration: 800,
                  mask: true
                })
                _this.setData({
                  gotoPay_payStatus: 1,
                })
                wx.showModal({
                  title: '支付成功',
                  content: '支付成功请返回首页',
                  showCancel: false,
                  confirmText: '返回首页',
                  success(res) {
                    if (res.confirm) {
                      wx.exitMiniProgram({
                        success: function () { },
                        fail: function () { }
                      })
                    } else if (res.cancel) {

                    }
                  }
                })
              }, 3000)
            },
            fail(res) {
              wx.showToast({
                title: '取消支付',
                icon: 'error',
                duration: 500,
                mask: true
              })
            },
            complete(res) {
              if (res.errMsg == 'requestPayment:cancel') {
                wx.showToast({
                  title: '取消支付',
                  icon: 'error',
                  duration: 500,
                  mask: true
                })
              }
            }
          })
        } else {
          wx.showToast({
            title: res.data.err_code_des || res.data.return_msg || '订单参数错误',
            icon: 'none',
            duration: 1000,
            mask: true
          })
        }

      }
    })
  },
  discountFun() {
    let that = this
    let e = that.data.baseUrl + "/switch/wxShopOver"
    let d = {
      site: that.data.p8site,
    }
    let n = (t) => {
      console.error("rrr1-------------", t);
      t = that.dateOrRes(t);
      console.error("discountFun", JSON.stringify(t));
      if (t.result === 0) {
        console.log(t.data);
        that.setData({
          discount: Number(t.data.advertisement_id)
        })
      } else {
        console.error("加载discountFun异常", t);
      }
    };
    that.wxRequest(e, "GET", d, n);
  },
  wxRequest(e, t, a, i, n) {
    wx.request({
      url: e,
      method: t,
      data: a,
      success: (e) => {
        if (i) {
          i(e);
        }
      },
      fail: (e) => {
        if (n) {
          n(e);
        }
      },
    });
  },
  dateOrRes(e) {
    return e.data ? e.data : e;
  },
  keyValueConnect(e) {
    let t = "";
    for (const i in e) {
      if (e.hasOwnProperty.call(e, i)) {
        const r = e[i];
        t += i + "=" + r + "&";
      }
    }
    t = t.substring(0, t.length - 1);
    t = encodeURI(t);
    return t;
  }
})