// pages/link/link.js
import {
  Base64
} from '../../base64';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    baseUrl: "https://trecharge.play800.cn",
    queryUrl: "/sy/activityKf",
    path: "",
    site: "",
    base: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let obj = wx.getEnterOptionsSync()
    console.error('-----------onLoadgetEnterOptionsSync-link', obj);
    console.error('-----------onLoadgetEnterOptionsSync-link-query', obj.query);
    if (Object.keys(obj.query).length == 0) {
      obj = wx.getLaunchOptionsSync();
      console.error('-----------onLoadgetLaunchOptionsSync-link', obj);
    }
    console.log('obj', obj);
    let site = "";
    let platform_url = "";
    let base = obj.query.base;
    console.log('base', base);
    let decodedMsg = Base64.decode(base);
    let linkData = JSON.parse(decodedMsg)
    console.log('linkData', linkData);
    if (linkData.site) {
      site = linkData.site
      platform_url = linkData.url ? linkData.url : this.data.baseUrl
    }
    console.log(' [site] > ', site);
    console.log(' [platform_url] > ', platform_url);
    console.log(' [base] > ', base);
    let url = `${platform_url}${this.data.queryUrl}?site=${site}`
    console.log(' [url] > ', url);

    let isLinkActive = wx.getStorageSync('isLinkActive')
    if (isLinkActive) {
      wx.redirectTo({
        url: '/pages/index/index',
      })

      return;
    }

    this.setData({
      path: url,
      site: site,
      baseUrl: platform_url,
      base: base,
    })

    wx.setStorageSync('isLinkActive', true)

    wx.setClipboardData({
      data: url,
      success(res) {
        console.log('setClipboardData', res) // data
        wx.hideToast();
      }
    })
    wx.hideHomeButton();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          path: `/pages/link/link?base=${that.data.base}`,
          webpageUrl: that.data.path,
        })
      }, 2000)
    })
    return {
      path: `/pages/link/link?base=${that.data.base}`,
      webpageUrl: that.data.path,
      promise
    }
  },
  onShareTimeline() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          query: `base=${that.data.base}`,
        })
      }, 2000)
    })
    return {
      query: `base=${that.data.base}`,
      promise
    }
  }
})