/**app.wxss**/

page {
  --primary-color: #4a90e2;
  --danger-color: #ff6b6b;
  --text-color: #333;
  --text-secondary: #666;
  --text-light: #999;
  --bg-color: #f8f9fa;
  --card-bg: #ffffff;
  --border-radius: 16px;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --custom-font: 'CustomFont', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  /* 全屏背景图设置 */
  /* background-image: url('/assets/image/bg_01.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  min-height: 100vh; */
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: var(--bg-color);
}

/* 通用卡片样式 */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: var(--shadow);
}

/* 通用按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.2);
}

.btn-text {
  margin-left: 8px;
}

/* Toast提示 */
.toast {
  position: fixed;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  z-index: 1001;
}