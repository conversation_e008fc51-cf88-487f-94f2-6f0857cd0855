<scroll-view class="rookie-container" scroll-y="true" enhanced="true" show-scrollbar="false">
  <image src="{{backgroundImageUrl}}" mode="widthFix" class="rookie-container-image" />

  <!-- <view class="header">
    <image src="/assets/image/Tab_glxq.png" class="header-image" />
  </view> -->
  <!-- <view class="left-btn" style="top: {{menuButtonTop * 2 + 'rpx'}};height: {{menuButtonHeight * 2 + 'rpx'}};" bind:tap="gotoBack">
    <image src="/assets/image/Button_fh.png" mode="aspectFit" class="left-btn-image"></image>
  </view> -->

  <!-- <view class="title" style="height:{{ menuButtonHeight * 2 + 'rpx' }}; line-height: {{menuButtonHeight * 2 + 'rpx'}};top: {{menuButtonTop * 2 + 'rpx'}};">{{navBarTitle}}</view> -->
</scroll-view>