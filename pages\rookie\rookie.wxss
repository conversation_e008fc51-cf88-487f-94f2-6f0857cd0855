/* pages/rookie/rookie.wxss */
.rookie-container {
  width: 100%;
  height: 100vh; /* 使用视口高度 */
  overflow: hidden; /* scroll-view自己处理滚动 */
  background-color: #1f1925;
}

.rookie-container-image {
  width: 100%;
  display: block; /* 确保图片正确显示 */
  /* 移除height限制，让图片根据自身比例显示 */
}

.header {
  position: absolute;
  top: 0;
  width: 100%;
  height: 114rpx;
}

.header-image {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.left-btn {
  position: absolute;
  left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1999;
}

.left-btn-image {
  width: 78rpx;
  height: 48rpx;
}

.title {
  position: absolute;
  width: 100%;
  top: 40rpx;
  text-align: center;
  font-size: 32rpx;
  color: #ffffff;
  z-index: 1;
}