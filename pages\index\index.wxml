<!--pages/index/index.wxml-->
<view class="index-container">
  <view class="index-container-bg">
    <image src="/assets/image/bg_01.jpg" class="index-container-img"></image>
  </view>

  <view class="menu-container">
    <view class="menu-list">
      <view class="menu-item {{item.checked ? 'menu-item-selected' : ''}} {{index === 4 ? 'menu-item-half' : ''}}" wx:for="{{menuList}}" wx:key="index" bind:tap="menuClick" data-type="{{item.type}}" data-title="{{item.title}}">
        <image src="/assets/image/btn_ty.png" class="menu-item-bg" />
        <image src="{{item.iconUrl}}" class="menu-item-icon" style="width: {{item.width}}; height: {{item.height}};bottom: {{item.bottom}};" />
        <view class="menu-title">{{item.title}}</view>
      </view>
    </view>
  </view>

  <view class="slider-container">
    <view class="slider-list">
      <view class="slider-item" wx:for="{{sliderList}}" wx:key="index">
        <image src="/assets/image/Slider_hover.png" class="slider-dot" wx:if="{{item.active}}" />
        <image src="/assets/image/Slider_normal.png" class="slider-dot" wx:else="" />
      </view>
    </view>
  </view>

  <view class="info-container">
    <view class="info-container-bg">
      <image src="/assets/image/Card_01.png" class="info-container-img" />
    </view>
    <view class="info-list">
      <!-- 显示筛选后的列表，如果没有筛选则显示所有 -->
      <view class="info-item" wx:for="{{selectedMenuType ? filteredInfoList : infoList}}" wx:key="index" bind:tap="infoClick" data-id="{{item.id}}" data-title="{{item.title}}">
        <view class="info-item-title">{{item.title}}</view>
        <view class="info-item-subTitle">{{item.subTitle}}</view>
        <image src="/assets/image/Card02.png" class="info-item-image-bg" />
        <image src="{{item.imgUrl}}" mode="aspectFill" class="info-item-image" />
        <image src="/assets/image/xt_xsgl_ty.png" class="info-item-image-xt" />
        <view class="info-container-content">{{item.content}}</view>
      </view>
      <!-- 当筛选后没有数据时显示提示 -->
      <view class="no-data-tip" wx:if="{{selectedMenuType && filteredInfoList.length === 0}}">
        <text>暂无相关内容</text>
      </view>
    </view>
  </view>

</view>