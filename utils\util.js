// 工具函数

// 格式化时间
const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('-')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

// 格式化数字
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 生成唯一ID
const generateUniqueId = prefix => {
  return prefix + '_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
}

// 截取文本
const truncateText = (text, length) => {
  if (!text) return '';
  if (text.length <= length) return text;
  return text.substring(0, length) + '...';
}

// 去除HTML标签
const removeHtmlTags = html => {
  if (!html) return '';
  return html.replace(/<[^>]+>/g, '');
}

// 显示Toast提示
const showToast = (title, icon = 'none') => {
  wx.showToast({
    title: title,
    icon: icon
  });
}

// 显示加载提示
const showLoading = title => {
  wx.showLoading({
    title: title,
    mask: true
  });
}

// 隐藏加载提示
const hideLoading = () => {
  wx.hideLoading();
}

// 显示确认对话框
const showConfirm = (title, content) => {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title: title,
      content: content,
      success: res => {
        if (res.confirm) {
          resolve(true);
        } else {
          resolve(false);
        }
      },
      fail: err => {
        reject(err);
      }
    });
  });
}

module.exports = {
  formatTime,
  formatNumber,
  generateUniqueId,
  truncateText,
  removeHtmlTags,
  showToast,
  showLoading,
  hideLoading,
  showConfirm
}