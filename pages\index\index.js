// pages/index/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active: 1,
    activeMenu: null,
    selectedMenuType: null, // 当前选中的菜单类型
    menuList: [{
        title: "新手攻略",
        iconUrl: "../../assets/image/icon_xsgl.png",
        id: 1,
        pid: 1,
        width: "82rpx",
        height: "62rpx",
        bottom: "60rpx",
        type: "rookie",
        checked: false,
      },
      {
        title: "关卡技巧",
        iconUrl: "../../assets/image/icon_gkjq.png",
        id: 2,
        pid: 1,
        width: "56rpx",
        height: "70rpx",
        bottom: "60rpx",
        type: "level",
        checked: false,
      },
      {
        title: "职业攻略",
        iconUrl: "../../assets/image/icon_zygl.png",
        id: 3,
        pid: 1,
        width: "66rpx",
        height: "64rpx",
        bottom: "60rpx",
        type: "profession",
        checked: false,
      },
      {
        title: "调研问卷",
        iconUrl: "../../assets/image/icon_dywj.png",
        id: 4,
        pid: 1,
        width: "82rpx",
        height: "38rpx",
        bottom: "70rpx",
        type: "survey",
        checked: false,
      },
      {
        title: "小助手",
        iconUrl: "../../assets/image/icon_xzs.png",
        id: 5,
        pid: 1,
        width: "72rpx",
        height: "72rpx",
        bottom: "55rpx",
        type: "assistant",
        checked: false,
      },
    ],
    sliderList: [{
        id: 1,
        active: false,
      },
      {
        id: 2,
        active: false,
      },
      {
        id: 3,
        active: false,
      }
    ],
    infoList: [{
        id: 1,
        title: "新手攻略",
        subTitle: "新手首日快速通过技巧",
        imgUrl: "../../assets/image/banner_xsgl_01.png",
        content: "西游道阻且长，伏魔之路漫漫·····此番西游伏魔众妖聚首，他们各个都身怀绝技，本领过人而本次",
        type: ["rookie"]
      },
      {
        id: 2,
        title: "艾丹",
        subTitle: "暗夜英雄秘传 影舞者·艾丹",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/aidan/aidanBanner.png",
        content: "以一敌百，成功守护精灵族的圣地，族人心中的传奇英雄，他又有着怎么样神秘的能力呢?",
        type: ["profession"]
      },
      {
        id: 3,
        title: "菲欧娜",
        subTitle: "站桩输出流 吸血鬼·菲欧娜",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/fiona/fionaBanner.jpg",
        content: "东方装束的包子头吸血鬼?纤弱的少女竟然肉过牛头人?离谱的人设搭配更加离谱的躺平流派设计",
        type: ["profession"]
      },
      {
        id: 4,
        title: "维克托",
        subTitle: "法术机关枪 蒸汽机器人·维克托",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/victory/victoryBanner.jpg",
        content: "机械降神维克托，主神圣元素伤害",
        type: ["profession"]
      },
      {
        id: 5,
        title: "影",
        subTitle: "带狼高富帅 限定英雄·影",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/role/shadow/shadowBanner.png",
        content: "游戏首个传奇级别英雄，自带召唤物【雷牙】",
        type: ["profession"]
      },
      {
        id: 6,
        title: "符文",
        subTitle: "萌新向符文系统详情",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/rune/runeBanner.jpg",
        content: "符文系统是游戏中的一个重要系统，玩家可以通过收集符文来提升自己的战斗力",
        type: ["rookie", 'level']
      },
      {
        id: 7,
        title: "装备",
        subTitle: "进阶向装备构筑详解",
        imgUrl: "https://dhbhtoss.jvplay.cn/siyu/gear/gearBanner.jpg",
        detailImageUrl: "https://dhbhtoss.jvplay.cn/siyu/gear/gearDetail.jpg",
        content: "二件套攻略不是噱头但仅适用于初入深渊章节，处于红装向粉装过渡阶段的玩家，此阶段之前还是套装更强",
        type: ["rookie", 'level']
      },
    ],
    filteredInfoList: [] // 筛选后的信息列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this;
    that.changeSliderList("sliderList", that.data.sliderList);
    that.changeMenuList("menuList", that.data.menuList);
    let obj = wx.getEnterOptionsSync()
    console.error('-----------onLoadgetEnterOptionsSync-index', obj);
    console.error('-----------onLoadgetEnterOptionsSync-index-query', obj.query);
    if (Object.keys(obj.query).length == 0) {
      obj = wx.getLaunchOptionsSync();
      console.error('-----------onLoadgetLaunchOptionsSync-index', obj);
    }

    let isOfficialActive = wx.getStorageSync('isOfficialActive')
    if (isOfficialActive) {
      return;
    }

    const scene = obj.scene
    if (scene == 1037 && obj.referrerInfo.extraData.uid) {
      console.log('[ 满足条件 ] >')
      wx.navigateTo({
        url: `/pages/official/official`
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    let that = this;
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          path: `/pages/index/index`,
          webpageUrl: 'https://dhbhtoss.jvplay.cn/siyu/role/victory/victoryBanner.jpg',
        })
      }, 2000)
    })
    return {
      path: `/pages/index/index`,
      webpageUrl: 'https://dhbhtoss.jvplay.cn/siyu/role/victory/victoryBanner.jpg',
      promise
    }
  },
  onShareTimeline() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({})
      }, 2000)
    })
    return {
      promise
    }
  },
  // 修改data数据
  changeData(key, value) {
    let that = this;
    that.setData({
      [key]: value
    });
  },
  // 修改menuList数据
  changeMenuList(key, value) {
    let that = this;
    let filterList = that.data.menuList.filter(item => item.pid === that.data.active)
    that.changeData(key, filterList)
  },
  // 修改sliderList数据
  changeSliderList(key, value) {
    let that = this;
    that.data.sliderList.forEach(item => {
      if (item.id === that.data.active) {
        item.active = true;
      }
    });
    that.changeData(key, value)
  },
  // 菜单点击
  menuClick(e) {
    let that = this;
    let {
      type
    } = e.currentTarget.dataset;
    let {
      title
    } = e.currentTarget.dataset

    if (type === 'survey') {
      wx.showToast({
        title: '该功能暂未开放',
        icon: 'none',
        duration: 2000
      })
      return;
    }

    if (type === "assistant") {
      wx.navigateTo({
        url: `/pages/${type}/${type}?title=${title}`
      })
    } else {
      // 判断是否点击的是当前已选中的菜单项
      if (that.data.selectedMenuType === type) {
        // 取消选中状态
        that.clearMenuSelection();
      } else {
        // 更新菜单选中状态
        that.updateMenuCheckedStatus(type);
        // 筛选信息列表
        that.filterInfoList(type);
        // 设置当前选中的菜单类型
        that.changeData("selectedMenuType", type);
        that.changeData("activeMenu", type);
      }
    }
  },
  // 更新菜单选中状态
  updateMenuCheckedStatus(selectedType) {
    let that = this;
    let updatedMenuList = that.data.menuList.map(item => {
      return {
        ...item,
        checked: item.type === selectedType
      };
    });
    that.changeData("menuList", updatedMenuList);
  },
  // 筛选信息列表
  filterInfoList(selectedType) {
    let that = this;
    let filteredList = that.data.infoList.filter(item => item.type.includes(selectedType));
    that.changeData("filteredInfoList", filteredList);
  },
  // 清除菜单选中状态
  clearMenuSelection() {
    let that = this;
    // 清除所有菜单的选中状态
    let updatedMenuList = that.data.menuList.map(item => {
      return {
        ...item,
        checked: false
      };
    });
    that.changeData("menuList", updatedMenuList);
    // 清除选中的菜单类型
    that.changeData("selectedMenuType", null);
    that.changeData("activeMenu", null);
    // 清空筛选列表，恢复显示全部数据
    that.changeData("filteredInfoList", []);
  },
  // 详情点击
  infoClick(e) {
    let {
      id
    } = e.currentTarget.dataset;
    let {
      title
    } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/rookie/rookie?id=${id}&title=${title}`
    })
  }
})