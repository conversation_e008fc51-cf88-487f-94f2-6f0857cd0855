// pages/webview/webview.js
// let P8SDK = require('../../p8sdk-wechat-1.0.51')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    baseUrl: "https://trecharge.play800.cn",
    queryUrl: "/sy/activityKf",
    path: "",
    site: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let obj = wx.getEnterOptionsSync()
    console.error('-----------onLoadgetEnterOptionsSync-webview', obj);
    console.error('-----------onLoadgetEnterOptionsSync-webview-query', obj.query);
    if (Object.keys(obj.query).length == 0) {
      obj = wx.getLaunchOptionsSync();
      console.error('-----------onLoadgetLaunchOptionsSync-webview', obj);
    }
    let site = "";
    let platform_url = "";
    if (obj.referrerInfo.extraData) {
      site = obj.referrerInfo.extraData.site;
      platform_url = obj.referrerInfo.extraData.url ? obj.referrerInfo.extraData.url : this.data.baseUrl;
    }
    if (obj.query.site) {
      site = obj.query.site
      platform_url = obj.query.url ? obj.query.url : this.data.baseUrl
    }
    console.log(' [site] > ', site);
    console.log(' [platform_url] > ', platform_url);
    let url = `${platform_url}${this.data.queryUrl}?site=${site}`
    console.log(' [url] > ', url);

    let isWebviewActive = wx.getStorageSync('isWebviewActive')
    if (isWebviewActive) {
      wx.redirectTo({
        url: '/pages/index/index',
      })

      return;
    }

    this.setData({
      path: url,
      site: site,
      baseUrl: platform_url,
    })

    wx.setStorageSync('isWebviewActive', true)

    wx.setClipboardData({
      data: url,
      success(res) {
        console.log('setClipboardData', res) // data
        wx.hideToast();
      }
    })


    // if (P8SDK) {
    //   P8SDK.login().then((res) => {
    //     console.log(' [P8SDK.sdkData] >', P8SDK.sdkData);
    //     let platform_url = P8SDK.sdkData.platform_url ? P8SDK.sdkData.platform_url : this.data.baseUrl
    //     console.log(' [platform_url] > ', platform_url);
    //     let url = `${this.data.baseUrl}${this.data.queryUrl}?site=${site}`
    //     // let url = `${platform_url}${this.data.queryUrl}?site=${site}`
    //     console.log(' [url] > ', url);
    //     this.setData({
    //       path: url,
    //       site: site,
    //       baseUrl: platform_url,
    //     })
    //     console.error('webPath--->', this.data.path);
    //   })
    // }

    wx.hideHomeButton();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady(options) {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          path: `/pages/webview/webview?site=${that.data.site}&url=${that.data.baseUrl}`,
          webpageUrl: that.data.path,
        })
      }, 2000)
    })
    return {
      path: `/pages/webview/webview?site=${that.data.site}&url=${that.data.baseUrl}`,
      webpageUrl: that.data.path,
      promise
    }
  },
  onShareTimeline() {
    let that = this
    const promise = new Promise(resolve => {
      setTimeout(() => {
        resolve({
          query: `site=${that.data.site}&url=${that.data.baseUrl}`,
        })
      }, 2000)
    })
    return {
      query: `site=${that.data.site}&url=${that.data.baseUrl}`,
      promise
    }
  }
})