<!-- Generated by SpecStory -->

# 将签到功能改为观看视频获取金币 (2025-06-16 11:04:04)

_**User**_

我现在有个需求，想把签到这个改成观看激励视频获取金币

---

_**Assistant**_

[MODE: RESEARCH]

我正在分析您的需求，需要将签到功能改为观看激励视频获取金币的功能。

首先，让我理解当前的签到功能实现：

当前的签到功能是通过 `signIn` 函数实现的，它会检查用户是否已经在今天签到过，如果没有，则增加1个金币并更新存储和UI显示。

要实现观看激励视频获取金币的功能，我们需要使用微信小程序的激励视频广告组件 `wx.createRewardedVideoAd`。这个API允许开发者展示激励视频广告，用户完整观看后可以获得奖励。

我需要了解的关键点：

1. 您是否已经在微信小程序管理后台配置了激励视频广告单元ID？
2. 每次观看视频想要奖励多少金币？
3. 是否需要保留每日限制逻辑，比如每天只能观看一定次数的激励视频？

接下来，我将基于现有代码设计一个观看激励视频获取金币的功能方案。

[MODE: INNOVATE]

基于对现有代码的分析，我提出以下解决方案：

### 方案概述

将原有的签到功能改为观看激励视频获取金币功能，主要涉及以下几个方面：

1. 创建和初始化激励视频广告组件
2. 处理广告加载、展示、关闭等事件
3. 在用户成功观看完广告后给予金币奖励
4. 优化用户体验，包括加载提示、错误处理等

### 技术实现要点

1. 使用 `wx.createRewardedVideoAd()` API 创建激励视频广告实例
2. 监听广告的 `onLoad`、`onError` 和 `onClose` 事件
3. 在 `onClose` 事件中判断用户是否完整观看了广告，如果是则发放金币奖励
4. 使用 `videoAd.show()` 展示广告
5. 适当的错误处理和用户提示

### 优势

- 为小程序增加了变现渠道
- 用户可以通过主动行为获得更多金币
- 相比每日签到，可以多次观看获取更多奖励，增加用户粘性

### 考虑因素

- 需要在微信小程序管理后台配置激励视频广告
- 可能需要考虑观看次数限制，防止滥用
- 需要处理网络问题或广告加载失败的情况

[MODE: PLAN]

基于前面的分析和创新思考，我制定以下实施计划：

### 实施计划：将签到功能改为观看激励视频获取金币

首先，我们需要修改 `pages/profile/profile.js` 文件，将原有的签到功能替换为观看激励视频获取金币的功能。

**详细步骤：**

1. 在 data 中添加激励视频广告相关的配置项
2. 在 onLoad 中初始化激励视频广告组件
3. 修改原有的 signIn 函数为 watchAdForCoins 函数
4. 处理广告的加载、错误和关闭事件
5. 在用户完整观看广告后给予金币奖励
6. 更新页面 UI 中的相关文本，从"签到"改为"看广告得金币"

**实施清单：**

Implementation Checklist:
1. 在 data 对象中添加 adUnitId 和 videoAd 变量
2. 在 onLoad 函数中添加初始化激励视频广告的代码
3. 将 signIn 函数重命名为 watchAdForCoins 并修改其实现
4. 在 watchAdForCoins 函数中实现显示激励视频广告的逻辑
5. 添加错误处理和用户体验优化代码
6. 修改 WXML 文件中的相关按钮文本（可选，取决于您的页面结构）

[MODE: EXECUTE]

执行清单项 1-5（由于未提供 WXML 文件，我们先专注于 JS 文件的修改）：

```javascript:pages/profile/profile.js
// ... existing code ...

data: {
  userInfo: {},
  userAgent: '',
  balance: 0,
  showRechargePopup: false,
  selectedAmount: null,
  customAmount: '',
  coinOptions: [{
      amount: 6,
      price: 6
    },
    {
      amount: 30,
      price: 30
    },
    {
      amount: 98,
      price: 98
    },
    {
      amount: 128,
      price: 128
    },
    {
      amount: 198,
      price: 198
    },
    {
      amount: 328,
      price: 328
    },
    {
      amount: 648,
      price: 648
    }
  ],
  baseUrl: 'https://center.lekplay.cn',
  basePayUrl: 'https://recharge.lekplay.cn',
  p8site: '',
  p8key: '',
  // 添加激励视频广告ID
  adUnitId: 'adunit-xxxxxxxxxxxxxxxx', // 请替换为您在小程序后台申请的广告单元ID
  videoAd: null,
},

// ... existing code ...

onLoad: function () {
  // 尝试获取用户信息
  this.isWechat();
  
  // 初始化激励视频广告
  if (wx.createRewardedVideoAd) {
    this.initRewardedVideoAd();
  } else {
    console.warn('当前版本不支持激励视频广告');
  }
},

// ... existing code ...

// 初始化激励视频广告
initRewardedVideoAd: function() {
  const videoAd = wx.createRewardedVideoAd({
    adUnitId: this.data.adUnitId
  });
  
  videoAd.onLoad(() => {
    console.log('激励视频广告加载成功');
  });
  
  videoAd.onError((err) => {
    console.error('激励视频广告加载失败', err);
  });
  
  this.setData({
    videoAd: videoAd
  });
},

// 观看激励视频获取金币（替换原有的签到功能）
watchAdForCoins: function () {
  const that = this;
  
  if (!this.data.videoAd) {
    wx.showToast({
      title: '广告组件加载失败',
      icon: 'none'
    });
    return;
  }
  
  wx.showLoading({
    title: '加载中...'
  });
  
  // 加载并展示激励视频广告
  this.data.videoAd.show().then(() => {
    wx.hideLoading();
  }).catch(err => {
    wx.hideLoading();
    // 重新加载广告
    this.data.videoAd.load()
      .then(() => this.data.videoAd.show())
      .catch(err => {
        console.error('激励视频广告显示失败', err);
        wx.showToast({
          title: '视频加载失败，请稍后再试',
          icon: 'none'
        });
      });
  });
  
  // 监听用户关闭广告事件
  this.data.videoAd.onClose((res) => {
    // 用户完整观看广告，发放奖励
    if (res && res.isEnded) {
      // 更新金币余额（这里设置为+5金币，您可以根据需要调整）
      const currentBalance = wx.getStorageSync('balance') || 0;
      const rewardCoins = 5; // 每次观看奖励5金币
      const newBalance = currentBalance + rewardCoins;
      wx.setStorageSync('balance', newBalance);
      
      // 更新页面显示的余额
      that.setData({
        balance: newBalance
      });
      
      wx.showToast({
        title: `观看完成 +${rewardCoins}金币`,
        icon: 'success'
      });
    } else {
      // 用户提前关闭广告，不发放奖励
      wx.showToast({
        title: '需完整观看视频才能获得奖励',
        icon: 'none'
      });
    }
    
    // 移除关闭事件监听器，防止多次触发
    that.data.videoAd.offClose();
  });
},

// ... existing code ...
```

现在我们需要将原有的 `signIn` 函数替换为新的 `watchAdForCoins` 函数：

```javascript:pages/profile/profile.js
// ... existing code ...

// 注意：以下代码替换了原有的 signIn 函数
// 每日签到功能改为观看激励视频获取金币
watchAdForCoins: function () {
  const that = this;
  
  if (!this.data.videoAd) {
    wx.showToast({
      title: '广告组件加载失败',
      icon: 'none'
    });
    return;
  }
  
  wx.showLoading({
    title: '加载中...'
  });
  
  // 加载并展示激励视频广告
  this.data.videoAd.show().then(() => {
    wx.hideLoading();
  }).catch(err => {
    wx.hideLoading();
    // 重新加载广告
    this.data.videoAd.load()
      .then(() => this.data.videoAd.show())
      .catch(err => {
        console.error('激励视频广告显示失败', err);
        wx.showToast({
          title: '视频加载失败，请稍后再试',
          icon: 'none'
        });
      });
  });
  
  // 监听用户关闭广告事件
  this.data.videoAd.onClose((res) => {
    // 用户完整观看广告，发放奖励
    if (res && res.isEnded) {
      // 更新金币余额（这里设置为+5金币，您可以根据需要调整）
      const currentBalance = wx.getStorageSync('balance') || 0;
      const rewardCoins = 5; // 每次观看奖励5金币
      const newBalance = currentBalance + rewardCoins;
      wx.setStorageSync('balance', newBalance);
      
      // 更新页面显示的余额
      that.setData({
        balance: newBalance
      });
      
      wx.showToast({
        title: `观看完成 +${rewardCoins}金币`,
        icon: 'success'
      });
    } else {
      // 用户提前关闭广告，不发放奖励
      wx.showToast({
        title: '需完整观看视频才能获得奖励',
        icon: 'none'
      });
    }
    
    // 移除关闭事件监听器，防止多次触发
    that.data.videoAd.offClose();
  });
},

// ... existing code ...
```

[MODE: REVIEW]

我已完成将签到功能改为观看激励视频获取金币功能的实现。现在让我们对实现进行审查：

### 代码实现审查

1. **添加了广告相关配置**：在 data 对象中添加了 adUnitId 和 videoAd 变量
2. **初始化广告组件**：在 onLoad 函数中添加了初始化激励视频广告的代码
3. **替换签到功能**：将原有的 signIn 函数替换为 watchAdForCoins 函数
4. **完整的广告流程处理**：
   - 广告加载和显示
   - 错误处理和重试逻辑
   - 用户完整观看后的奖励发放
   - 用户提前关闭广告的处理
5. **用户体验优化**：添加了加载提示和结果反馈

### 实现要点

1. 使用 `wx.createRewardedVideoAd` 创建激励视频广告实例
2. 监听广告的 `onLoad`、`onError` 和 `onClose` 事件
3. 在用户完整观看广告后给予金币奖励（示例中设置为5金币，可根据需要调整）
4. 添加了错误处理和用户提示

### 注意事项

1. **广告单元ID**：您需要将代码中的 `adunit-xxxxxxxxxxxxxxxx` 替换为您在微信小程序管理后台申请的实际广告单元ID
2. **奖励金额**：当前设置为每次观看奖励5金币，您可以根据需要调整这个值
3. **WXML修改**：您需要在页面的WXML文件中将原有的"签到"按钮绑定的事件从 `signIn` 改为 `watchAdForCoins`，并可能需要更新按钮文本
4. **兼容性**：代码中已添加了对不支持激励视频广告的设备的检查

### 后续建议

1. 考虑添加观看次数限制，例如每天最多观看10次广告获取金币
2. 可以考虑添加首次观看奖励翻倍等激励机制
3. 监控广告观看数据，优化用户体验和收益

实现完美符合计划要求，成功将签到功能改为观看激励视频获取金币功能。

请您确认这些修改是否符合您的需求。如果需要进一步调整，例如修改奖励金额或添加观看次数限制，请告知。

---

