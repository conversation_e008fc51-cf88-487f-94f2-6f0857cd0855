{"libVersion": "3.8.11", "projectname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": {"miniprogram": {"list": [{"name": "pages/index/index", "pathName": "pages/index/index", "query": "", "scene": 1037, "launchMode": "default", "referrerInfo": {"appId": "123", "extraData": "{\"uid\": \"123456789\",\"rold\":\"0987654321\" }"}}, {"name": "pages/vip/vip", "pathName": "pages/vip/vip", "query": "", "launchMode": "default", "scene": 1037, "referrerInfo": {"appId": "123", "extraData": "{\"site\": \"dgjd_data\" }"}}, {"name": "pages/assistant/assistant", "pathName": "pages/assistant/assistant", "query": "", "launchMode": "default", "scene": null}]}}, "setting": {"urlCheck": true, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": true, "useIsolateContext": true}}